【前置条件】执行monkey测试，
【测试步骤】monkey随机操作，
【预期结果】正常
【实际结果】抛出异常
【车机版本】SGMW_F710S-01-CN-stable/f710s_4.00.01_20250725-V01R01B0255-2025.0814.2041
【VIN】LK61ECF4760000000
【详细log如下】
08-18 01:36:08.844 3289 4346 E AndroidRuntime: FATAL EXCEPTION: WeatherInfoService-com.sgmw.lingos.weather.manager.WeatherManger@4edd97a-Thread
08-18 01:36:08.844 3289 4346 E AndroidRuntime: Process: com.sgmw.lingos.weather, PID: 3289
08-18 01:36:08.844 3289 4346 E AndroidRuntime: DeadSystemException: The system died; earlier logs will point to the root cause
08-18 01:36:08.845 1061 6427 D BufferPoolAccessor2.0: bufferpool2 0xb400006f387db758 : 0(0 size) total buffers - 0(0 size) used buffers - 13/15 (recycle/alloc) - 2/14 (fetch/transfer)
08-18 01:36:08.845 690 16812 I SDM : HWCSession::HandlePluggableDisplays: Handling hotplug...
08-18 01:36:08.845 1061 6427 D BufferPoolAccessor2.0: Destruction - bufferpool2 0xb400006f387db758 cached: 0/0M, 0/0% in use; allocs: 15, 87% recycled; transfers: 14, 86% unfetched
08-18 01:36:08.846 1061 1207 D BufferPoolAccessor2.0: bufferpool2 0xb400006f387da4d8 : 0(0 size) total buffers - 0(0 size) used buffers - 6/7 (recycle/alloc) - 1/6 (fetch/transfer)
08-18 01:36:08.846 1061 6428 D BufferPoolAccessor2.0: bufferpool2 0xb400006f387e0a98 : 0(0 size) total buffers - 0(0 size) used buffers - 8/10 (recycle/alloc) - 2/9 (fetch/transfer)
08-18 01:36:08.846 1061 1207 D BufferPoolAccessor2.0: bufferpool2 0xb400006f387dc788 : 0(0 size) total buffers - 0(0 size) used buffers - 8/10 (recycle/alloc) - 3/9 (fetch/transfer)
08-18 01:36:08.846 1061 6428 D BufferPoolAccessor2.0: Destruction - bufferpool2 0xb400006f387e0a98 cached: 0/0M, 0/0% in use; allocs: 10, 80% recycled; transfers: 9, 78% unfetched
08-18 01:36:08.847 1061 1207 D BufferPoolAccessor2.0: Destruction - bufferpool2 0xb400006f387dc788 cached: 0/0M, 0/0% in use; allocs: 10, 80% recycled; transfers: 9, 67% unfetched
08-18 01:36:08.850 690 16812 W SDM : DRMConnector::GetInfo: Connector 61 not found.
08-18 01:36:08.850 690 16812 W SDM : HWInfoDRM::GetDisplaysStatus: Unknown display type = 0 on connector id 61.
08-18 01:36:08.850 690 16812 I SDM : HWCSession::operator(): Display commit pending on display 294-1
08-18 01:36:08.850 690 16812 I SDM : HWCSession::HandleConnectedDisplays: Pending display commit on one of the displays. Deferring display creation.
08-18 01:36:08.851 690 16812 I SDM : HWCSession::HandlePluggableDisplays: Handling hotplug... Done. Hotplug events pending.
08-18 01:36:08.851 0 0 I : [T16812:HwBinder:690_3@1] [drm-shd] shd_connector_get_modes: directly use base mode in DT
08-18 01:36:08.857 1061 6428 D BufferPoolAccessor2.0: Destruction - bufferpool2 0xb400006f387da4d8 cached: 0/0M, 0/0% in use; allocs: 7, 86% recycled; transfers: 6, 83% unfetched
08-18 01:36:08.857 1061 1207 D BufferPoolAccessor2.0: bufferpool2 0xb400006f387df5c8 : 0(0 size) total buffers - 0(0 size) used buffers - 8/11 (recycle/alloc) - 3/10 (fetch/transfer)
08-18 01:36:08.857 1061 1207 D BufferPoolAccessor2.0: Destruction - bufferpool2 0xb400006f387df5c8 cached: 0/0M, 0/0% in use; allocs: 11, 73% recycled; transfers: 10, 70% unfetched
08-18 01:36:08.857 1061 1207 D BufferPoolAccessor2.0: bufferpool2 0xb400006f387df818 : 0(0 size) total buffers - 0(0 size) used buffers - 12/15 (recycle/alloc) - 3/14 (fetch/transfer)
08-18 01:36:08.858 1061 1207 D BufferPoolAccessor2.0: Destruction - bufferpool2 0xb400006f387df818 cached: 0/0M, 0/0% in use; allocs: 15, 80% recycled; transfers: 14, 79% unfetched
08-18 01:36:08.878 690 16813 I SDM : HWCSession::HandlePluggableDisplays: Handling hotplug...
08-18 01:36:08.880 690 16813 W SDM : DRMConnector::GetInfo: Connector 61 not found.
08-18 01:36:08.881 690 16813 W SDM : HWInfoDRM::GetDisplaysStatus: Unknown display type = 0 on connector id 61.
08-18 01:36:08.881 690 16813 I SDM : HWCSession::operator(): Display commit pending on display 294-1
08-18 01:36:08.881 690 16813 I SDM : HWCSession::HandleConnectedDisplays: Pending display commit on one of the displays. Deferring display creation.
08-18 01:36:08.881 690 16813 I SDM : HWCSession::HandlePluggableDisplays: Handling hotplug... Done. Hotplug events pending.
08-18 01:36:08.851 0 0 I : [T16812:HwBinder:690_3@1] [drm-shd] shd_connector_get_modes: directly use base mode in DT
08-18 01:36:08.881 0 0 I : [T16813:HwBinder:690_3@3] [drm-shd] shd_connector_get_modes: directly use base mode in DT
08-18 01:36:08.887 2128 2128 D PropertyIdUtils: 获取ResultActions 回调的propertyId = 559940320
08-18 01:36:08.887 2128 2128 I VehicleSettingModel: onChangeEvent: Float value:0.0,propertyId:559940320,funcId:2
08-18 01:36:08.887 2128 2128 D VehicleControlConfigRepository: onChangeEvent: 2 0.0
08-18 01:36:08.888 629 629 E Zygote : Zygote failed to write to system_server FD: Connection refused
08-18 01:36:08.888 629 629 I Zygote : Process 1228 exited due to signal 6 (Aborted)
08-18 01:36:08.888 629 629 E Zygote : Exit zygote because system server (pid 1228) has terminated
08-18 01:36:08.888 2384 2384 D AndroidRuntime: Shutting down VM
08-18 01:36:08.889 2384 2384 E AndroidRuntime: FATAL EXCEPTION: main
08-18 01:36:08.889 2384 2384 E AndroidRuntime: Process: com.sgmw.themestore, PID: 2384
08-18 01:36:08.889 2384 2384 E AndroidRuntime: DeadSystemException: The system died; earlier logs will point to the root cause
08-18 01:36:08.890 14231 14231 W Monkey : ** Failed talking with activity manager!
08-18 01:36:08.890 14231 14231 W Monkey : ** Error: RemoteException while injecting event.
08-18 01:36:08.890 14231 14231 I Monkey : Events injected: 273
08-18 01:36:08.891 4447 4655 E AndroidRuntime: FATAL EXCEPTION: DiagManager
08-18 01:36:08.891 4447 4655 E AndroidRuntime: Process: com.sgmw.cardiaghmi, PID: 4447
08-18 01:36:08.891 4447 4655 E AndroidRuntime: DeadSystemException: The system died; earlier logs will point to the root cause
08-18 01:36:08.891 751 14192 W AudioFlinger: power manager service died !!!
08-18 01:36:08.891 751 14192 I chatty : uid=1041(audioserver) Binder:751_E identical 4 lines
08-18 01:36:08.891 751 14192 W AudioFlinger: power manager service died !!!
08-18 01:36:08.891 1789 30965 D Property.service: binderDied android.os.BinderProxy@510ed4d
08-18 01:36:08.891 751 14192 W AudioFlinger: power manager service died !!!