package com.sgmw.lingos.weather.manager;

import static android.app.UiModeManager.MODE_NIGHT_NO;
import static android.app.UiModeManager.MODE_NIGHT_YES;
import static android.car.Car.POWER_SERVICE;
import static com.sgmw.lingos.weather.utils.Constants.DESC2CODE;
import static com.sgmw.lingos.weather.utils.Constants.SETTING_UI_NIGHT_MODE;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_CITY;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_LAT;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_LON;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_SUNRISE_INFO;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_SUNSET_INFO;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_WINFO;
import static com.sgmw.lingos.weather.utils.Constants.UI_AUTO_MODE;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.app.UiModeManager;
import android.car.Car;
import android.car.annotation.AddedInOrBefore;
import android.car.hardware.power.CarPowerManager;
import android.car.hardware.property.CarPropertyManager;
import android.content.Context;
import android.content.SharedPreferences;
import android.database.ContentObserver;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.DeadSystemException;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresPermission;

import com.blankj.utilcode.util.NetworkUtils;
import com.google.gson.reflect.TypeToken;
import com.sgmw.entity.Hourly;
import com.sgmw.entity.HourlyDataItem;
import com.sgmw.entity.WeatherData;
import com.sgmw.lingos.weather.R;
import com.sgmw.lingos.weather.WeatherApp;
import com.sgmw.lingos.weather.callback.WeatherInfoCallback;
import com.sgmw.lingos.weather.callback.WeatherNotifyCallBack;
import com.sgmw.lingos.weather.entity.CardData;
import com.sgmw.lingos.weather.helper.NetHelper;
import com.sgmw.lingos.weather.helper.PrefUtils;
import com.sgmw.lingos.weather.helper.WeatherProvider;
import com.sgmw.lingos.weather.utils.Constants;
import com.sgmw.lingos.weather.utils.DateUtil;
import com.sgmw.lingos.weather.utils.GsonUtil;
import com.sgmw.lingos.weather.utils.LogUtils;
import com.sgmw.lingos.weather.utils.MyNetworkUtil;
import com.sgmw.navi.external.CarNaviManager;
import com.sgmw.navi.external.INaviSetting;
import com.sgmw.navi.external.INaviSettingListener;
import com.sgmw.navi.external.interfaces.NaviStatusListener;
import com.sgmw.permissionsdk.PermissionChangeListener;
import com.sgmw.permissionsdk.PermissionManager;
import com.sgmw.permissionsdk.bean.PermissionApp;
import com.sgmw.permissionsdk.bean.PermissionGroups;
import com.sgmw.widget.SGMWToast;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

import timber.log.Timber;

public class WeatherManger implements WeatherInfoCallback, CarPowerManager.CarPowerStateListener, NaviStatusListener {
    private String TAG = "WeatherManger";
    private static WeatherManger instance;
    private LocationManager locationManager;//系统GPS定位管理类
    private HandlerThread mHandlerThread;//工作线程
    private Handler workHandler;//工作handler
    private static final int MSG_RETRY_INIT_WEATHER = 119;//重新设置位置监听
    private static final int MSG_GET_LOCATION_TIMEOUT = 120;//获取定位超时
    private static final int MSG_GET_NAV_TIMEOUT = 121;//调取导航接口超时
    private static final int LOCATION_TIMEOUT = 10 * 1000;//10秒定位超时
    private static final int WAITING_BING = 2 * 1000;//2秒后再次尝试
    private static final int LOCATION_IN = 10 *//10分钟
            60 *//分钟单位
            1000;//毫米单位
    private static final int MSG_RETRY_GET_WEATHER = 1119;//每十分钟获取天气消息
    private WeatherProvider provider;
    private static final String logTag = "WeatherManger";
    private static List<WeatherNotifyCallBack> mCallBackList = new ArrayList<>();
    private static Context mContext;
    volatile long lastSendTime = -MIN_UPDATE_INTERVAL;//保证第一次能够传递
    private static final long MIN_UPDATE_INTERVAL = 10_000;
    //车机无法获取到天气的温度时的信号值 155
    public static final String TEMP_ERROR_CODE = "155";
    public static final String NAV_ERROR_CODE = "-1";
    /**
     * 高德地图是否初始化完成
     */
    private boolean hasMapInit = false;

    /**
     * 是否需要重新初始化标志位
     */
    private volatile boolean needsReinitialization = false;

    Handler handler = new Handler(Objects.requireNonNull(Looper.myLooper())) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            if (msg.what == MSG_RETRY_GET_WEATHER) {
                Log.i(logTag, "WeatherInfoService postdelay called--");
                getNavAreaInfo();
                try {
                    if (PermissionManager.getInstance().checkPermission(mContext.getPackageName(), Manifest.permission_group.LOCATION) && naviSetting == null) {
                        handler.sendEmptyMessageDelayed(MSG_RETRY_GET_WEATHER, WAITING_BING);
                        return;
                    }
                } catch (RemoteException e) {
                    LogUtils.e(TAG, e);
                }
                handler.sendEmptyMessageDelayed(MSG_RETRY_GET_WEATHER, LOCATION_IN);
            } else if (msg.what == MSG_GET_NAV_TIMEOUT) {
                provider.notifyCityChange(NAV_ERROR_CODE);
                refreshWeatherOnce();
            }
        }
    };

    public static WeatherManger getInstance(Context context) {
        if (instance == null || isInstanceInvalid()) {
            synchronized (WeatherManger.class) {
                if (instance == null || isInstanceInvalid()) {
                    // 如果实例存在但状态无效，先清理旧实例
                    if (instance != null) {
                        LogUtils.i("WeatherManger", "Cleaning up invalid instance");
                        instance.cleanup();
                    }
                    mContext = context;
                    instance = new WeatherManger();
                }
            }
        }
        return instance;
    }

    /**
     * 检查实例是否有效
     */
    private static boolean isInstanceInvalid() {
        if (instance == null) return true;

        // 检查关键组件是否正常
        return instance.mHandlerThread != null && !instance.mHandlerThread.isAlive();
    }

    private Car mCarApi;
    private CarPropertyManager mCarPropertyManager;
    private CarPowerManager mCarPowerManager;

    CarNaviManager mCarNaviManager;
    INaviSetting naviSetting;

    private WeatherManger() {
        String processName = getProcessName(mContext, android.os.Process.myPid());
        Timber.i(logTag + "WeatherInfoService onCreate processName:" + processName);
        if (processName != null) {
            boolean defaultProcess = processName.equals("com.sgmw.lingos.weather");
            if (defaultProcess) {
                //当前应用的初始化
                if (locationManager == null) {
                    locationManager = (LocationManager) mContext.getSystemService(Context.LOCATION_SERVICE);
                }
                Timber.i(logTag + processName + ":WeatherInfoService onCreate");
                NetHelper.getInstance().init(mContext);//初始化nethelper
                PrefUtils.getInstance().initSpUtils(mContext, processName);//初始化SP

                mHandlerThread = new HandlerThread("WeatherInfoService-" + this + "-Thread");
                mHandlerThread.start();
                workHandler = new Handler(mHandlerThread.getLooper()) {
                    @Override
                    public void handleMessage(Message msg) {
                        if (msg.what == MSG_RETRY_INIT_WEATHER) {
                            Timber.w(logTag + "MSG_RETRY_INIT_WEATHER");
                            initWeather();
                        } else if (msg.what == MSG_GET_LOCATION_TIMEOUT) {
                            Timber.i(logTag + " MSG_GET_LOCATION_TIMEOUT");
                            if (WeatherApp.isWeatherApp())
                                SGMWToast.makeToast(WeatherApp.getInstance(), WeatherApp.getInstance().getString(R.string.get_location_fail)).show();
                            refreshWeather();
                        }
                    }
                };

                provider = new WeatherProvider();
                provider.setmWeatherInfoCallback(this);
                workHandler.sendEmptyMessageDelayed(MSG_RETRY_INIT_WEATHER, 3000);

                if (mCarApi != null && mCarApi.isConnected()) {
                    mCarApi.disconnect();
                    mCarApi = null;
                }
                mCarApi = Car.createCar(mContext, null, Car.CAR_WAIT_TIMEOUT_DO_NOT_WAIT, (car, ready) -> {
                    if (ready) {
                        initManage(car);
                    }
                });
            }
        }
        mCarNaviManager = CarNaviManager.create(mContext);
        mCarNaviManager.registerNaviListener(this);
        registerPermission();
        registerNetWorkStatusChanged();

        //监听系统设置->显示->主题设置
        //如果切到自动模式设置一下白天黑夜模式
        ContentObserver settingUINightMode = new ContentObserver(new Handler(Looper.getMainLooper())) {
            @Override
            public void onChange(boolean selfChange, @Nullable Uri uri) {
                super.onChange(selfChange, uri);
                Log.i(TAG, "settingUINightMode Change!!!!!!!!! ");
                setThemeMode();
            }
        };
        mContext.getContentResolver().registerContentObserver(Settings.System.getUriFor(SETTING_UI_NIGHT_MODE), false, settingUINightMode);
    }

    /**
     * TICE Network Temperature
     * Requires permission: {@link Car#PERMISSION_CAR_INFO}.
     */
    @RequiresPermission(Car.PERMISSION_CAR_INFO)
    @AddedInOrBefore(majorVersion = 33)
    public static final int TICE_NETWORK_TEMPERATURE = 559939966;

    private void initManage(Car car) {
        Log.i(TAG, "init CarPropertyManager");
        mCarPropertyManager = (CarPropertyManager) car.getCarManager(Car.PROPERTY_SERVICE);
        mCarPowerManager = (CarPowerManager) car.getCarManager(POWER_SERVICE);
        mCarPowerManager.setListener(this);

        // Car服务连接成功，检查是否需要重新初始化
        onCarServiceConnected();
    }

    public void setTemp2Appearance(String temp) {
        try {
            if (mCarPropertyManager != null) {
                Log.i(TAG, "216 setTemp2Appearance: 仪表数据" + temp);
                mCarPropertyManager.setFloatProperty(TICE_NETWORK_TEMPERATURE, 0, Float.parseFloat(temp));
            } else {
                Log.i(TAG, "218 setTemp2Appearance: carservice == null");
            }
        } catch (Exception e) {
            Log.e(TAG, "222 setTemp2Appearance: 分支异常");
            Log.e(TAG, "222 setTemp2Appearance: ", e);
        }
    }


    public void refreshWeather() {
        Log.i(logTag, "WeatherInfoService postdelay called");
        try {
            String lat = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_LAT);
            String lon = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_LON);
            Timber.i(logTag + "requestWInfoByLocationFirst lat lon get");
            provider.requestWeatherByPosition(String.valueOf(lat), String.valueOf(lon), "", "normal_", input -> {
            });
        } catch (Exception e) {
            Timber.e("%sException:requestWInfoByLocationFirst", logTag);
            Timber.e(e);
        }
    }

    public void refreshWeatherOnce() {
        initLocationListenerOnce();
    }

    private void initWeather() {
        initWeatherWithProtection();
    }

    /**
     * 带保护机制的天气初始化方法
     */
    private void initWeatherWithProtection() {
        try {
            // 检查系统服务状态
            if (!isSystemServiceAvailable()) {
                LogUtils.w(TAG, "System service not available, skipping weather initialization");
                markForReinitialization();
                return;
            }

            performWeatherInitialization();
        } catch (DeadSystemException e) {
            LogUtils.e(TAG, "System service died during weather initialization: " + e.getMessage());
            // 标记需要重新初始化，等待下次触发时机
            markForReinitialization();
        } catch (Exception e) {
            LogUtils.e(TAG, "Unexpected error during weather initialization: " + e.getMessage());
        }
    }

    /**
     * 执行实际的天气初始化逻辑
     */
    private void performWeatherInitialization() {
        String processName = getProcessName(mContext, android.os.Process.myPid());
        Timber.i(logTag + "WeatherInfoService initWeather processName:" + processName);
        if (processName != null) {
            boolean defaultProcess = processName.equals("com.sgmw.lingos.weather");
            if (defaultProcess) {
                try {
                    if (!PermissionManager.getInstance().checkPermission(mContext.getPackageName(), Manifest.permission_group.LOCATION)) {
                        Timber.w(logTag + "initLocationListener failed, no permission");
                        // 标记需要重新初始化，等待权限获取
                        markForReinitialization();
                        return;
                    }
                } catch (RemoteException e) {
                    Log.e(TAG, "182 initLocationListener: ", e);
                    // 标记需要重新初始化，等待权限服务恢复
                    markForReinitialization();
                    return;
                }
                if (workHandler.hasMessages(MSG_RETRY_INIT_WEATHER)) {
                    workHandler.removeMessages(MSG_RETRY_INIT_WEATHER);
                }
                handler.sendEmptyMessage(MSG_RETRY_GET_WEATHER);
            }
        }
    }

    /**
     * 请求一次 onceLocationListener
     */
    public void initLocationListenerOnce() {
        //规避app启动后执行多次
        String processName = getProcessName(mContext, android.os.Process.myPid());
        Timber.i(logTag + "WeatherInfoService initLocationListenerOnce processName:" + processName);
        if (processName != null) {
            boolean defaultProcess = processName.equals("com.sgmw.lingos.weather");
            if (defaultProcess) {
                //当前应用的初始化
                try {
                    if (!PermissionManager.getInstance().checkPermission(mContext.getPackageName(), Manifest.permission_group.LOCATION)) {
                        Timber.w(logTag + "initLocationListenerOnce failed, no permission");
                        return;
                    }
                } catch (RemoteException e) {
                    Log.e(TAG, "182 initLocationListenerOnce: ", e);
                    return;
                }

                List<String> providers = locationManager.getProviders(true);
                String locationProvider;
                if (providers.contains(LocationManager.GPS_PROVIDER)) {
                    locationProvider = LocationManager.GPS_PROVIDER;
                } else if (providers.contains(LocationManager.NETWORK_PROVIDER)) {
                    locationProvider = LocationManager.NETWORK_PROVIDER;
                } else {
                    Timber.e(logTag + "initLocationListener failed, no Providers find");
                    return;
                }
                locationManager.removeUpdates(onceLocationListener);
                locationManager.requestLocationUpdates(locationProvider, 1000, 0, onceLocationListener, workHandler.getLooper());//放在工作线程（子线程）
                workHandler.sendEmptyMessageDelayed(MSG_GET_LOCATION_TIMEOUT, LOCATION_TIMEOUT);
            }
        }
    }


    /**
     * 只使用一次的定位回调
     */
    private final LocationListener onceLocationListener = new LocationListener() {

        @Override
        public void onStatusChanged(String provider, int status, Bundle extras) {
            Timber.d("%s onceLocationListener onStatusChanged %s", logTag, provider);
        }

        @Override
        public void onProviderEnabled(String provider) {
            Timber.d("%s onceLocationListener onProviderEnabled %s", logTag, provider);
        }

        @Override
        public void onProviderDisabled(String provider) {
            Timber.d("%s onceLocationListener onProviderDisabled %s", logTag, provider);
            //定位失败，移除超时警告
            if (workHandler.hasMessages(MSG_GET_LOCATION_TIMEOUT)) {
                workHandler.removeMessages(MSG_GET_LOCATION_TIMEOUT);
            }
            workHandler.sendEmptyMessage(MSG_GET_LOCATION_TIMEOUT);

        }

        @SuppressLint("MissingPermission")
        @Override
        public void onLocationChanged(Location loc) {
            //规避app启动后执行多次
            String processName = getProcessName(mContext, android.os.Process.myPid());
            Timber.i(logTag + "WeatherInfoService onLocationChanged processName:" + processName);
            if (processName != null) {
                boolean defaultProcess = processName.equals("com.sgmw.lingos.weather");
                if (defaultProcess) {
                    //定位成功，移除超时警告
                    if (workHandler.hasMessages(MSG_GET_LOCATION_TIMEOUT)) {
                        workHandler.removeMessages(MSG_GET_LOCATION_TIMEOUT);
                    }

                    double lat = loc.getLatitude();
                    double lon = loc.getLongitude();
                    String cityDetail = "";
                    try {
                        cityDetail = tryGetCityDetail(loc);
                    } catch (Exception e) {
                        Timber.e("%sException:onLocationChanged", logTag);
                    } finally {
                        PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_LAT, String.valueOf(lat));
                        PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_LON, String.valueOf(lon));
//                        Timber.d("onLocationChanged {lat:%s,lon:%s}, cityDetail:%s", lat, lon, cityDetail);
                        locationManager.removeUpdates(onceLocationListener);
                        long sysTime = System.currentTimeMillis();
                        String time = "time:" + sysTime + "_";
                        lastSendTime = -MIN_UPDATE_INTERVAL;
                        provider.requestWeatherByPosition(String.valueOf(lat), String.valueOf(lon), cityDetail, time, input -> {
                        });
                    }
                }
            }
        }
    };

    public void setCallback(WeatherNotifyCallBack callback) {
        if (!mCallBackList.contains(callback)) {
            mCallBackList.add(callback);
        }
    }

    public void removeCallBack(WeatherNotifyCallBack callback) {
        if (mCallBackList.contains(callback)) {
            mCallBackList.remove(callback);
        }
    }

    /*
     * 获取城市详情，用于展示定位城市信息
     * */
    private String tryGetCityDetail(Location location) {
        String cityInfo = "";
        try {
            if (location != null) {
                Geocoder gc = new Geocoder(mContext, Locale.getDefault());
                List<Address> result = gc.getFromLocation(location.getLatitude(), location.getLongitude(), 1);
                if (result != null && result.size() > 0) {
                    Timber.v(logTag + "getAddress :%s", result.toString());
                    Address address = result.get(0);
                    try {
                        cityInfo = address.getAddressLine(0);

                        //拼接出来省+市+区.如果这些拿不到就用地址
                        if (!TextUtils.isEmpty(address.getAdminArea())//省
                                && !TextUtils.isEmpty(address.getLocality())//市
                                && !TextUtils.isEmpty(address.getSubLocality())) {//区
//                            if (address.getAdminArea().contains("北京")
//                                    || address.getAdminArea().contains("上海")
//                                    || address.getAdminArea().contains("重庆")
//                                    || address.getAdminArea().contains("天津")) {
                            //直辖市的省和市是一样的，不需要重复拼接
                            //统一格式，只要市+区
                            cityInfo = address.getLocality() + address.getSubLocality();
//                            } else {
//                                cityInfo = address.getAdminArea() + address.getLocality() + address.getSubLocality();
//                            }
                        }
                    } catch (Exception e) {
                        Timber.e("%sException:tryGetCityDetail", logTag);
                        //e.printStackTrace();
                    }
                    Timber.i(logTag + "mCurrentCity:%s", cityInfo);
                }
            }
        } catch (Exception e) {
            Timber.e("%sException:tryGetCityDetail", logTag);
            //e.printStackTrace();
        }
        return cityInfo;
    }

    /**
     * @param cxt
     * @param pid
     * @return 获取进程名称
     */
    public static String getProcessName(Context cxt, int pid) {
        ActivityManager am = (ActivityManager) cxt.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> runningApps = am.getRunningAppProcesses();
        if (runningApps == null) {
            return null;
        }
        for (ActivityManager.RunningAppProcessInfo procInfo : runningApps) {
            if (procInfo.pid == pid) {
                return procInfo.processName;
            }
        }
        return null;
    }

    @Override
    public void onCityGetComplete(String city) {
        if (mCallBackList != null) {
            synchronized (mCallBackList) {
                for (int i = 0; i < mCallBackList.size(); i++) {
                    WeatherNotifyCallBack weatherNotifyCallBack = mCallBackList.get(i);
                    weatherNotifyCallBack.notifyChangeCityInfo(city);
                }
            }
        }
    }

    @Override
    public void onWeatherGetComplete(WeatherData weatherData) {
        notifyWeatherChange(weatherData);
        notifyOtherMode(weatherData);
    }

    /**
     * 通知MainActivity 天气是否有更新
     *
     * @param weatherData
     */
    private void notifyWeatherChange(WeatherData weatherData) {
        if (mCallBackList != null) {
            synchronized (mCallBackList) {
                for (int i = 0; i < mCallBackList.size(); i++) {
                    WeatherNotifyCallBack weatherNotifyCallBack = mCallBackList.get(i);
                    weatherNotifyCallBack.notifyChangeWeatherInfo(mContext, weatherData);
                }
            }
        }
    }

    /**
     * 通知其他模块 天气更新
     *
     * @param weatherData
     */
    private void notifyOtherMode(WeatherData weatherData) {
        if (SystemClock.elapsedRealtime() - lastSendTime > MIN_UPDATE_INTERVAL) {
            lastSendTime = SystemClock.elapsedRealtime();
            //通知其他模块
            if (weatherData != null) {
                HourlyDataItem firstHourWeather = getFirstHourWeather(weatherData.getHourly());
                Log.i(TAG, "366 onWeatherGetComplete: " + firstHourWeather.toString());
                setTemp2Appearance(firstHourWeather.getTemp());
                
                // 使用显式广播进行跨应用通信
                sendWeatherRefreshBroadcast(weatherData, firstHourWeather);
                
                Log.i(TAG, "134:notifyChangeWeatherInfo:  " + weatherData.toString());
            } else {
                setTemp2Appearance(TEMP_ERROR_CODE);
                // 通知天气获取失败
                sendWeatherFailureBroadcast();
                Log.i(TAG, "139:notifyChangeWeatherInfo:  ");
            }
        } else {
            Log.i(TAG, "374:onWeatherGetComplete: 10000ms has send not respond ");
        }
    }

    /**
     * 发送天气刷新广播
     */
    private void sendWeatherRefreshBroadcast(WeatherData weatherData, HourlyDataItem firstHourWeather) {
        try {
            // 使用广播管理器发送广播
            Bundle bundle = new Bundle();
            bundle.putString("weatherCode", DESC2CODE.get(firstHourWeather.getWeatherDesc()) + "");
            bundle.putString("sunrise", weatherData.getSunUp() + "");
            bundle.putString("sunset", weatherData.getSunDown() + "");
            bundle.putString("weatherTemp", firstHourWeather.getTemp());
            bundle.putString("location", weatherData.getCityNameStr() + "-" + weatherData.getDistrictName());
            bundle.putString("province", weatherData.getProvinceName());
            bundle.putLong("timestamp", System.currentTimeMillis());
            
            BroadcastManager.getInstance(mContext).sendWeatherRefreshBroadcast(bundle);
            
            // 同时更新 SharedPreferences 供其他应用读取
            updateWeatherSharedPreferences(weatherData, firstHourWeather);
            
        } catch (Exception e) {
            LogUtils.e(TAG, "sendWeatherRefreshBroadcast: 发送广播失败"+e);
        }
    }

    /**
     * 发送天气获取失败广播
     */
    private void sendWeatherFailureBroadcast() {
        try {
            SharedPreferences prefs = mContext.getSharedPreferences("weather_data", Context.MODE_PRIVATE);
            Bundle bundle = new Bundle();
            bundle.putString("weatherCode",  prefs.getString("weatherCode",""));
            bundle.putString("sunrise", prefs.getString("sunrise",""));
            bundle.putString("sunset", prefs.getString("sunset",""));
            bundle.putString("weatherTemp",  prefs.getString("weatherTemp",""));
            bundle.putString("location",  prefs.getString("location",""));
            bundle.putString("province",  prefs.getString("province",""));
            bundle.putLong("timestamp", System.currentTimeMillis());

            BroadcastManager.getInstance(mContext).sendWeatherRefreshBroadcast(bundle);

        } catch (Exception e) {
            LogUtils.e(TAG, "sendWeatherFailureBroadcast: 发送广播失败"+e);
        }
    }

    /**
     * 更新天气数据到 SharedPreferences
     */
    private void updateWeatherSharedPreferences(WeatherData weatherData, HourlyDataItem firstHourWeather) {
        try {
            SharedPreferences prefs = mContext.getSharedPreferences("weather_data", Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString("weatherCode", DESC2CODE.get(firstHourWeather.getWeatherDesc()) + "");
            editor.putString("sunrise", weatherData.getSunUp() + "");
            editor.putString("sunset", weatherData.getSunDown() + "");
            editor.putString("weatherTemp", firstHourWeather.getTemp());
            editor.putString("location", weatherData.getCityNameStr() + "-" + weatherData.getDistrictName());
            editor.putString("province", weatherData.getProvinceName());
            editor.putLong("timestamp", System.currentTimeMillis());
            editor.putString("status", "success");
            editor.apply();
            
            LogUtils.i(TAG, "updateWeatherSharedPreferences: 天气数据已更新到 SharedPreferences");
        } catch (Exception e) {
            LogUtils.e(TAG, "updateWeatherSharedPreferences: 更新失败"+e);
        }
    }

    public HourlyDataItem getFirstHourWeather(List<Hourly> list) {
        if (list == null || list.size() == 0) {
            Log.i(TAG, "386 getFirstHourWeather: ");
            return new HourlyDataItem("12:00", "24", R.mipmap.ic_cloudy, "晴");
        }
        Hourly hourly = list.get(0);
        String con_temp = hourly.getTemp();
        String con_hour = hourly.getHour();
        if (con_hour.length() < 2) {
            con_hour = "0" + con_hour + ":00";
        } else {
            con_hour = con_hour + ":00";
        }
        String hourly_str = hourly.getCondition();
        Map<String, Integer> weatherNameDesIcon;
        Timber.i("%s hourly date : %s , hour : %s , is night : %s", logTag, hourly.getDate(), hourly.getHour(), hourly.isIconIsNight());
        if (!hourly.isIconIsNight()) {
            weatherNameDesIcon = Constants.WEATHER_NAME_DES_ICON_DAY;
        } else {
            weatherNameDesIcon = Constants.WEATHER_NAME_DES_ICON_NIGHT;
        }
        int con_weatherImageResId;
        if (hourly_str != null && weatherNameDesIcon.containsKey(hourly_str)) {
            Integer temp_hourly_str = weatherNameDesIcon.get(hourly_str);
            con_weatherImageResId = temp_hourly_str == null ? R.mipmap.ic_sunny : temp_hourly_str;
        } else {
            con_weatherImageResId = R.mipmap.ic_sunny;
        }
        return new HourlyDataItem(con_hour, con_temp, con_weatherImageResId, hourly_str);
    }

    @Override
    public void onStateChanged(int i) {
        LogUtils.i(TAG, "onStateChanged==========: " + i);
        switch (i) {
            case CarPowerManager.STATE_ON:
                getNavAreaInfo();
                break;
            default:
                break;
        }
    }

    @Override
    public void onNotifyNaviStatus(int status) {
        LogUtils.i(TAG, "onNotifyNaviStatus: status = " + status);
        if (status == CarNaviManager.MSG_NAVI_CONNECT) {
            try {
                naviSetting = mCarNaviManager.getNaviSetting();
                naviSetting.registerListener(mINaviSettingListener); //请求区域信息 naviSetting.naviAreaInfo();
            } catch (RemoteException e) {
                LogUtils.e(TAG, "onNotifyNaviStatus: e = " + e.getMessage());
            }
        }
    }

    private INaviSettingListener mINaviSettingListener = new INaviSettingListener.Stub() {
        @Override
        public void onChange(int nMode) throws RemoteException {
        }

        //区域信息回调
        @Override
        public void onNotifyAreaInfo(String data) throws RemoteException {
            LogUtils.i(TAG, "onNotifyAreaInfo:" + data);
            if (!hasMapInit) {
                lastSendTime = -MIN_UPDATE_INTERVAL; //确保这次的数据可以通知到其他模块
                hasMapInit = true;
            }
            if (handler.hasMessages(MSG_GET_NAV_TIMEOUT)) {
                handler.removeMessages(MSG_GET_NAV_TIMEOUT);
            }
            if (NAV_ERROR_CODE.equals(data)) {
                // -1 为导航约定的失败的返回值
                provider.notifyCityChange(NAV_ERROR_CODE);
                refreshWeatherOnce();
                return;
            }
            try {
                JSONObject jsonObject = new JSONObject(data);
                StringBuilder stringBuilder = new StringBuilder();
                if (jsonObject.has("CITY_NAME")) {
                    String cityName = jsonObject.getString("CITY_NAME");
                    if (!TextUtils.isEmpty(cityName)) {
                        stringBuilder.append(cityName);
                    }
                }
                if (jsonObject.has("AREA_NAME")) {
                    String areaName = jsonObject.getString("AREA_NAME");
                    if (!TextUtils.isEmpty(areaName)) {
                        if (!TextUtils.isEmpty(stringBuilder)) {
                            stringBuilder.append("-");
                        }
                        stringBuilder.append(areaName);
                    }
                }
                if (jsonObject.has("LATITUDE") && jsonObject.has("LONGITUDE")) {
                    double latitude = jsonObject.getDouble("LATITUDE");
                    double longitude = jsonObject.getDouble("LONGITUDE");
                    LogUtils.i(TAG, "latitude:" + latitude + " longitude:" + longitude);
                    PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_LAT, String.valueOf(latitude));
                    PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_LON, String.valueOf(longitude));

                }
                LogUtils.i(TAG, "stringBuilder.toString() :" + stringBuilder);
                provider.notifyCityChange(stringBuilder.toString());
                refreshWeather();
                return;
            } catch (JSONException e) {
                LogUtils.e(TAG, e.getMessage());
            }
            provider.notifyCityChange(NAV_ERROR_CODE);
            refreshWeatherOnce();
        }
    };

    public void getNavAreaInfo() {
        changeCardStatus(2);
        try {
            //有权限的情况
            if (PermissionManager.getInstance().checkPermission(mContext.getPackageName(), Manifest.permission_group.LOCATION)) {
                if (naviSetting != null) {
                    LogUtils.i(TAG, "getNavAreaInfo-----");
                    boolean connected = MyNetworkUtil.getInstance().isConnected();
                    LogUtils.i(TAG, "getNavAreaInfo----- connected:" + connected);
                    if (connected) {
                        naviSetting.naviAreaInfo();
                        if (handler.hasMessages(MSG_GET_NAV_TIMEOUT)) {
                            handler.removeMessages(MSG_GET_NAV_TIMEOUT);
                        }
                        handler.sendEmptyMessageDelayed(MSG_GET_NAV_TIMEOUT, LOCATION_TIMEOUT);
                        if (!hasMapInit) {
                            refreshWeather();
                        }
                    } else {
                        String weatherStr = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_WINFO);
                        LogUtils.i(TAG, "weatherStr == ");
                        if (WeatherApp.isWeatherApp()) {
                            SGMWToast.makeToast(WeatherApp.getInstance(), WeatherApp.getInstance().getString(R.string.get_data_fail)).show();
                        }
                        if (!TextUtils.isEmpty(weatherStr)) {
                            WeatherData weatherData = GsonUtil.fromJson(weatherStr, WeatherData.class);
                            if (null != weatherData) {
                                //天气页面不需要更新数据，MainActivity有自己处理缓存的逻辑
                                notifyWeatherChange(null);
                                notifyOtherMode(weatherData);
                                CardData cardData = conver2CardData(weatherData);
                                changeCardStatus(4, cardData);
                                return;
                            }
                        }
                        changeCardStatus(3);
                        onWeatherGetComplete(null);
                    }
                } else {
                    LogUtils.e(TAG, "naviSetting = null");
                }
            } else {
                //没有权限的时候
                LogUtils.i(TAG, "getNavAreaInfo----- no permission");
                onWeatherGetComplete(null);
                changeCardStatus(1);
            }
        } catch (RemoteException e) {
            LogUtils.e(TAG, e);
        }
    }

    public CardData conver2CardData(WeatherData weatherData) {
        CardData cardData = new CardData();
        String sLastLocationCity = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_CITY);
        Timber.i(logTag + "initView sLastLocationCity = ");
        if (!TextUtils.isEmpty(sLastLocationCity)) {
            cardData.setLocation(sLastLocationCity);
        } else {
            cardData.setLocation("");
        }

        //服务器当前时间
        long mCurrentTime = System.currentTimeMillis();
        boolean isNight;
        if (mCurrentTime < weatherData.getSunDown() && mCurrentTime > weatherData.getSunUp()) {
            isNight = false;
        } else {
            isNight = true;
        }
        cardData.setNight(isNight);
        cardData.setWeather(weatherData.getHourly().get(0).getCondition());
        cardData.setMaxTemp(weatherData.getTempDay() + "℃");
        cardData.setMinTemp(weatherData.getTempNight() + "℃");
        cardData.setDesc("空气" + weatherData.getAqi());
        cardData.setTemp(weatherData.getTemperature());
        cardData.setMoon(DateUtil.getMoon());
        cardData.setDay(DateUtil.getDay());
        cardData.setWeek(DateUtil.getWeekChina());
        return cardData;
    }

    private PermissionChangeListener permissionChangeListener = new PermissionChangeListener() {
        @Override
        public void permissionChanges(String data) {
            LogUtils.i(TAG, "PermissionChangeListener " + data);
            try {
                List<PermissionApp> permissionApps = GsonUtil.fromJson(data, new TypeToken<List<PermissionApp>>() {
                }.getType());
                for (PermissionApp app : permissionApps) {
                    if (TextUtils.equals(app.packageName, mContext.getPackageName())) {
                        for (PermissionGroups groups : app.permissionGroups) {
                            if (TextUtils.equals(groups.groupName, Manifest.permission_group.LOCATION)) {
                                if (groups.isGrant) {
                                    // 权限获取成功，检查是否需要重新初始化
                                    onPermissionGranted();
                                }
                                lastSendTime = -MIN_UPDATE_INTERVAL;//保证这次请求一定会通知到其他模块
                                getNavAreaInfo();
                                LogUtils.i(TAG, "PermissionChangeListener groups.isGrant:" + groups.isGrant);
                                return;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                LogUtils.e(TAG, "162 permissionChanges: " + e.getMessage());
            }
        }
    };

    private void registerPermission() {
        try {
            PermissionManager.getInstance().registerChangeListener(permissionChangeListener);
        } catch (RemoteException e) {
            LogUtils.e(TAG, "167 registerPermission: " + e.getMessage());
        }
    }

    private void registerNetWorkStatusChanged() {
        NetworkUtils.registerNetworkStatusChangedListener(mNetworkStatusChangedListener);
    }

    private final NetworkUtils.OnNetworkStatusChangedListener mNetworkStatusChangedListener = new NetworkUtils.OnNetworkStatusChangedListener() {
        @Override
        public void onDisconnected() {
            LogUtils.i(TAG, "onDisconnected: ");
        }

        @Override
        public void onConnected(NetworkUtils.NetworkType networkType) {
            LogUtils.i(TAG, "onConnected: networkType = " + networkType);

            // 如果之前因为系统服务异常而失败，现在重新尝试
            if (needsReinitialization) {
                needsReinitialization = false;
                LogUtils.i(TAG, "Network connected, retrying weather initialization");
                initWeatherWithProtection();
            }

            getNavAreaInfo();
        }
    };

    /**
     * 1 没权限
     * 2 加载中
     * 3 加载失败
     * 4 显示天气信息
     */
    public void changeCardStatus(int status, CardData cardData) {
        // 使用显式广播进行跨应用通信
        sendCardStatusBroadcast(status, cardData);
        LogUtils.i(TAG, "changeCardStatus:  " + status);
    }

    /**
     * 发送卡片状态变化广播
     */
    private void sendCardStatusBroadcast(int status, CardData cardData) {
        try {
            Bundle bundle = new Bundle();
            bundle.putInt("weather_status", status);
            bundle.putString("weather_data", GsonUtil.toJson(cardData));
            bundle.putLong("timestamp", System.currentTimeMillis());
            
            BroadcastManager.getInstance(mContext).sendWeatherStatusBroadcast(bundle);
            
            // 同时更新 SharedPreferences 供其他应用读取
            updateStatusSharedPreferences(status, cardData);
            
        } catch (Exception e) {
            LogUtils.e(TAG, "sendCardStatusBroadcast: 发送广播失败"+e);
        }
    }

    /**
     * 更新状态数据到 SharedPreferences
     */
    private void updateStatusSharedPreferences(int status, CardData cardData) {
        try {
            SharedPreferences prefs = mContext.getSharedPreferences("weather_status", Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putInt("weather_status", status);
            editor.putString("weather_data", GsonUtil.toJson(cardData));
            editor.putLong("timestamp", System.currentTimeMillis());
            editor.apply();
            
            LogUtils.i(TAG, "updateStatusSharedPreferences: 状态数据已更新到 SharedPreferences");
        } catch (Exception e) {
            LogUtils.e(TAG, "updateStatusSharedPreferences: 更新失败"+ e);
        }
    }

    /**
     * 1 没权限
     * 2 加载中
     * 3 加载失败
     * 4 显示天气信息
     */
    public void changeCardStatus(int status) {
        changeCardStatus(status, null);
    }

    /**
     * 根据天气接口获取的日升日落时间
     * 设置系统白天黑夜模式 自动亮度
     */
    public void setThemeMode() {
        int currentThemeMode = Settings.System.getInt(mContext.getContentResolver(), SETTING_UI_NIGHT_MODE, UI_AUTO_MODE);
        LogUtils.i(TAG, "SUN_TIME currentThemeMode = " + currentThemeMode);
        //当前是自动模式
        if (currentThemeMode == UI_AUTO_MODE) {
            LogUtils.i(TAG, "SUN_TIME UI_AUTO_MODE ======================= 自动模式 ");
            Calendar calendar = Calendar.getInstance();
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int[] sunTimes = getWeatherSunTime();

            LogUtils.i(TAG, "SUN_TIME TimeChangeReceiver ：hour = " + hour + ", minute = " + minute);

//            if ((hour == sunTimes[0] && minute == sunTimes[1])) {
//                mUiModeManager.setNightMode(UiModeManager.MODE_NIGHT_NO);
//                LogUtils.i(TAG, "SUN_TIME 设置白天模式");
//            } else if ((hour == sunTimes[2] && minute == sunTimes[3])) {
//                mUiModeManager.setNightMode(UiModeManager.MODE_NIGHT_YES);
//                LogUtils.i(TAG, "SUN_TIME 设置暗夜模式");
//            }
            UiModeManager mUiModeManager = (UiModeManager) mContext.getSystemService(Context.UI_MODE_SERVICE);
            int uiMode = mUiModeManager.getNightMode();
            LogUtils.i(logTag, "SUN_TIME 上次设置 " + (uiMode == 1 ? "浅色" : "深色") +" 模式");
            if (hour <= sunTimes[0] && minute < sunTimes[1] || hour >= sunTimes[2] && minute > sunTimes[3]) {
                if (uiMode != UiModeManager.MODE_NIGHT_YES) {
                    mUiModeManager.setNightMode(UiModeManager.MODE_NIGHT_YES);
                    LogUtils.i(TAG, "SUN_TIME 设置深色模式");
                }
            } else {
                if (uiMode != UiModeManager.MODE_NIGHT_NO) {
                    mUiModeManager.setNightMode(UiModeManager.MODE_NIGHT_NO);
                    LogUtils.i(TAG, "SUN_TIME 设置浅色模式");
                }
            }
        } else {
            LogUtils.i(TAG, "SUN_TIME NOT UI_AUTO_MODE ========================");
        }
    }

    public int[] getWeatherSunTime() {
        LogUtils.i(TAG, "getWeatherSunTime: ...........");
        int[] sunTimes = new int[4];

        String sunrise = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_SUNRISE_INFO);
        String sunset = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_SUNSET_INFO);

        if (TextUtils.isEmpty(sunrise)) {
            sunTimes[0] = 6;
            sunTimes[1] = 0;
        } else {
            sunTimes[0] = Integer.parseInt((String) sunrise.subSequence(11, 13));
            sunTimes[1] = Integer.parseInt((String) sunrise.subSequence(14, 16));
        }

        if (TextUtils.isEmpty(sunset)) {
            sunTimes[2] = 18;
            sunTimes[3] = 0;
        } else {
            sunTimes[2] = Integer.parseInt((String) sunset.subSequence(11, 13));
            sunTimes[3] = Integer.parseInt((String) sunset.subSequence(14, 16));
        }

        // 调试用
//        sunTimes[0] = 8;
//        sunTimes[1] = 20;
//        sunTimes[2] = 13;
//        sunTimes[3] = 49;

        LogUtils.i(TAG, "SUN_TIME sunriseHour = " + sunTimes[0] + "  sunriseMinute = " + sunTimes[1] +
                "  sunsetHour = " + sunTimes[2] + " sunsetMinute = " + sunTimes[3]);
        return sunTimes;
    }

    public int getPresentThemeMode(int[] sunTimes) {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        LogUtils.i(TAG, "SUN_TIME getPresent Hour = " + hour + "  minute = " + minute);
        if (hour <= sunTimes[0] && minute < sunTimes[1] || hour >= sunTimes[2] && minute > sunTimes[3]) {
            return MODE_NIGHT_YES;
        } else {
            return MODE_NIGHT_NO;
        }
    }

    /**
     * 检查系统服务是否可用
     * @return true 如果系统服务可用，false 如果不可用
     */
    private boolean isSystemServiceAvailable() {
        try {
            // 检查LocationManager
            if (locationManager == null) {
                locationManager = (LocationManager) mContext.getSystemService(Context.LOCATION_SERVICE);
            }

            // 尝试调用一个轻量级方法来验证服务是否正常
            if (locationManager != null) {
                locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
                return true;
            }

            LogUtils.w(TAG, "LocationManager is null, system service not available");
            return false;
        } catch (DeadSystemException e) {
            LogUtils.e(TAG, "System service is dead: " + e.getMessage());
            return false;
        } catch (SecurityException e) {
            LogUtils.w(TAG, "No permission to access location service: " + e.getMessage());
            return false; // 权限问题也视为不可用
        } catch (Exception e) {
            LogUtils.e(TAG, "Error checking system service: " + e.getMessage());
            return false;
        }
    }

    /**
     * 标记需要重新初始化，在合适的时机重试
     */
    private void markForReinitialization() {
        // 设置标志位，在下次网络连接恢复或权限检查时重新尝试
        needsReinitialization = true;
        LogUtils.i(TAG, "Marked for reinitialization due to system service issues");
    }

    /**
     * 在权限获取成功后重新初始化
     */
    private void onPermissionGranted() {
        if (needsReinitialization) {
            needsReinitialization = false;
            LogUtils.i(TAG, "Permission granted, retrying weather initialization");
            initWeatherWithProtection();
        }
    }

    /**
     * 在Car服务连接成功后重新初始化
     */
    private void onCarServiceConnected() {
        if (needsReinitialization) {
            needsReinitialization = false;
            LogUtils.i(TAG, "Car service connected, retrying weather initialization");
            initWeatherWithProtection();
        }
    }

    /**
     * 清理资源，释放线程和监听器
     * 应在应用销毁时调用
     */
    public void cleanup() {
        try {
            LogUtils.i(TAG, "Starting WeatherManger cleanup");

            // 清理Handler消息
            if (workHandler != null) {
                workHandler.removeCallbacksAndMessages(null);
                LogUtils.i(TAG, "Cleared workHandler messages");
            }
            if (handler != null) {
                handler.removeCallbacksAndMessages(null);
                LogUtils.i(TAG, "Cleared handler messages");
            }

            // 移除位置监听器
            if (locationManager != null && onceLocationListener != null) {
                try {
                    locationManager.removeUpdates(onceLocationListener);
                    LogUtils.i(TAG, "Removed location listener");
                } catch (Exception e) {
                    LogUtils.e(TAG, "Error removing location listener: " + e.getMessage());
                }
            }

            // 断开Car API连接
            if (mCarApi != null && mCarApi.isConnected()) {
                try {
                    mCarApi.disconnect();
                    mCarApi = null;
                    LogUtils.i(TAG, "Disconnected Car API");
                } catch (Exception e) {
                    LogUtils.e(TAG, "Error disconnecting Car API: " + e.getMessage());
                }
            }

            // 注销导航监听器
            if (mCarNaviManager != null) {
                try {
                    // 注意：CarNaviManager可能没有提供unregisterNaviListener方法
                    // 如果API不支持注销，则只需要将引用置空
                    LogUtils.i(TAG, "CarNaviManager reference cleared (no unregister method available)");
                    mCarNaviManager = null;
                } catch (Exception e) {
                    LogUtils.e(TAG, "Error clearing navigation manager: " + e.getMessage());
                }
            }

            // 安全退出HandlerThread - 使用非阻塞方式
            if (mHandlerThread != null) {
                try {
                    mHandlerThread.quitSafely(); // 安全退出，不等待
                    mHandlerThread = null;
                    workHandler = null;
                    LogUtils.i(TAG, "HandlerThread quit safely");
                } catch (Exception e) {
                    LogUtils.e(TAG, "Error quitting HandlerThread: " + e.getMessage());
                }
            }

            LogUtils.i(TAG, "WeatherManger cleanup completed successfully");
        } catch (Exception e) {
            LogUtils.e(TAG, "Error during cleanup: " + e.getMessage());
        }
    }

}
